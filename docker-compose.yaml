services:
  frontend:
    image: ghcr.io/graduation-project-ptit/eap-frontend:main
    container_name: eap-frontend
    ports:
      - "3000:3000"
    networks:
      - eap-network

  web_server:
    image: nginx:stable-alpine
    container_name: eap-web_server
    ports:
      - "80:80"
    volumes:
      - ./deploy/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - eap-network

networks:
  eap-network:

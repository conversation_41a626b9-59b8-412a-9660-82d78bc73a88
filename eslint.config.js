// eslint.config.js
import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import tseslint from "typescript-eslint";

export default tseslint.config(
  {
    // --- Configuration for TypeScript files ---
    files: ["**/*.{ts,tsx}"],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended, // General TypeScript rules
      ...tseslint.configs.recommendedTypeChecked, // Crucial for type-aware rules
      // Optional: Add stricter or stylistic type-aware rules
      // ...tseslint.configs.strictTypeChecked,
      // ...tseslint.configs.stylisticTypeChecked,
    ],
    languageOptions: {
      parserOptions: {
        // This is where you link to your TypeScript project
        project: true, // Tells ESLint to find tsconfig.json relative to current directory
        tsconfigRootDir: import.meta.dirname, // Important for resolving 'project' path
      },
      ecmaVersion: 2020, // Or 'latest' for current features
      globals: {
        ...globals.browser,
        // Add any other globals your project uses (e.g., Node.js globals if applicable)
      },
    },
    plugins: {
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      // You don't need to explicitly add "@typescript-eslint" here if you're using tseslint.configs
    },
    rules: {
      // General ESLint rules, e.g., for import order, unused vars, etc.
      // 'no-unused-vars': 'off', // Turn off default ESLint rule if TypeScript is handling it
      // '@typescript-eslint/no-unused-vars': 'warn', // Use the TypeScript version

      // React Hooks rules
      ...reactHooks.configs.recommended.rules,
      "react-refresh/only-export-components": [
        "warn",
        { allowConstantExport: true },
      ],
      // Add any custom type-aware rules or overrides here
      // For example, to enforce strict boolean expressions:
      // '@typescript-eslint/strict-boolean-expressions': 'error',
    },
  },
  {
    // --- Configuration for files to ignore ---
    // It's generally better to place 'ignores' at the top level or in a separate config object
    // if it applies globally.
    // However, if it's specifically for *this* linting pass, it's fine here too.
    ignores: ["dist", "**/*.js", "**/*.jsx"], // Also ignore JS files if you only lint TS/TSX
  }
);
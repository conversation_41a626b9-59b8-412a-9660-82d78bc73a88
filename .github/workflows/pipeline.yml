name: Deploy Infra

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout Infra code
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: thanh-nguyen03
          password: ${{ secrets.GHCR_PAT }}

      - name: Pull latest Infra code on server
        run: |
          cd /home/<USER>/eap/infra
          git pull origin main

      - name: Deploy Docker Compose stack
        run: |
          cd /home/<USER>/eap/infra

          # Pull latest images based on your docker-compose.yml
          # This will pull the images from GHCR
          docker compose pull

          # Bring up the services, ensure no local build, and remove old containers
          docker compose up -d --no-build --remove-orphans

          echo "Deployment complete for Infra and all services!"

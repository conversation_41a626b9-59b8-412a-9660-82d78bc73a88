upstream frontend {
    server frontend:80;
}

# upstream backend {
#     server backend:8080;
# }

server {
    listen 80;
    server_name eap.thanhnd.site;

    location / {
        proxy_pass http://frontend/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # location /api/ {
    #     rewrite ^/api/(.*)$ /$1 break;
    #     proxy_pass http://backend;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    # }
}